// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$MovieStateCWProxy {
  MovieState moviesList(MoviesList? moviesList);

  MovieState moviedetail(MovieData? moviedetail);

  MovieState movieserie(MovieData? movieserie);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `MovieState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// MovieState(...).copyWith(id: 12, name: "My name")
  /// ````
  MovieState call({
    MoviesList? moviesList,
    MovieData? moviedetail,
    MovieData? movieserie,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfMovieState.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfMovieState.copyWith.fieldName(...)`
class _$MovieStateCWProxyImpl implements _$MovieStateCWProxy {
  const _$MovieStateCWProxyImpl(this._value);

  final MovieState _value;

  @override
  MovieState moviesList(MoviesList? moviesList) => this(moviesList: moviesList);

  @override
  MovieState moviedetail(MovieData? moviedetail) =>
      this(moviedetail: moviedetail);

  @override
  MovieState movieserie(MovieData? movieserie) => this(movieserie: movieserie);

  @override
  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `MovieState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// MovieState(...).copyWith(id: 12, name: "My name")
  /// ````
  MovieState call({
    Object? moviesList = const $CopyWithPlaceholder(),
    Object? moviedetail = const $CopyWithPlaceholder(),
    Object? movieserie = const $CopyWithPlaceholder(),
  }) {
    return MovieState(
      moviesList:
          moviesList == const $CopyWithPlaceholder()
              ? _value.moviesList
              // ignore: cast_nullable_to_non_nullable
              : moviesList as MoviesList?,
      moviedetail:
          moviedetail == const $CopyWithPlaceholder()
              ? _value.moviedetail
              // ignore: cast_nullable_to_non_nullable
              : moviedetail as MovieData?,
      movieserie:
          movieserie == const $CopyWithPlaceholder()
              ? _value.movieserie
              // ignore: cast_nullable_to_non_nullable
              : movieserie as MovieData?,
    );
  }
}

extension $MovieStateCopyWith on MovieState {
  /// Returns a callable class that can be used as follows: `instanceOfMovieState.copyWith(...)` or like so:`instanceOfMovieState.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$MovieStateCWProxy get copyWith => _$MovieStateCWProxyImpl(this);
}
