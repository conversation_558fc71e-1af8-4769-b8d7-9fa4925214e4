import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:logger/web.dart';
import 'package:movie_app/core/contains.dart';
import 'package:movie_app/cubit/movie_state.dart';
import 'package:movie_app/local/app_preferences.dart';
import 'package:movie_app/models/movie_theme.dart';
import 'package:movie_app/services/movie_services.dart';

class MovieCubit extends Cubit<MovieState> {
  MovieCubit() : super(MovieState());
  final Logger logger = Logger();

  Future<void> getMovies() async {
    try {
      final response = await MovieServices.getMovies();
      emit(state.copyWith(moviesList: response));
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> getMoviesSingle() async {
    try {
      final response = await MovieServices.getMoviesByTheme(
        Contains.SINGLE_MOVIES,
      );
      emit(state.copyWith(moviedetail: response));
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> getMoviesSerie() async {
    try {
      final response = await MovieServices.getMoviesByTheme(
        Contains.SERIES_MOVIES,
      );
      emit(state.copyWith(movieserie: response));
    } catch (e) {
      logger.e(e);
    }
  }
}
