import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:movie_app/cubit/settings_state.dart';
import 'package:movie_app/local/app_preferences.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SettingsCubit() : super(SettingsState());

  void setIsDarkMode(bool isDarkMode) {
    emit(state.copyWith(isDarkMode: isDarkMode));
    AppPreferences.setIsDarkMode(isDarkMode);
  }

  void getIsDarkMode() {
    emit(state.copyWith(isDarkMode: AppPreferences.instance.isDarkMode));
  }

  void setIsEnglish(Locale locale) {
    emit(state.copyWith(locale: locale));
    AppPreferences.setIsEnglish(locale.languageCode == 'en' ? true : false);
  }

  void getIsEnglish() {
    emit(
      state.copyWith(
        locale: AppPreferences.instance.isEnglish ? Locale('en') : Locale('vi'),
      ),
    );
  }
}
