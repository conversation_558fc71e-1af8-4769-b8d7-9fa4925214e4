import 'dart:ui';

import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
part 'settings_state.g.dart';

@CopyWith()
class SettingsState extends Equatable {
  const SettingsState({
    this.isDarkMode = false,
    this.locale = const Locale('en'),
  });
  final bool isDarkMode;
  final Locale locale;
  @override
  List<Object> get props => [isDarkMode, locale];
}
