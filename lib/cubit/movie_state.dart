import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
import 'package:movie_app/models/movie_list.dart';
import 'package:movie_app/models/movie_theme.dart';
part 'movie_state.g.dart';

@CopyWith()
class MovieState extends Equatable {
  const MovieState({this.moviesList, this.moviedetail, this.movieserie});
  final MoviesList? moviesList;
  final MovieData? moviedetail;
  final MovieData? movieserie;
  @override
  List<Object?> get props => [moviesList, moviedetail, movieserie];
}
