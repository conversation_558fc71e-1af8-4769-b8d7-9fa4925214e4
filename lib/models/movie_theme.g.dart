// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_theme.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MovieData _$MovieDataFromJson(Map<String, dynamic> json) => MovieData(
  status: json['status'] as bool?,
  msg: json['msg'] as String?,
  data:
      json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MovieDataToJson(MovieData instance) => <String, dynamic>{
  'status': instance.status,
  'msg': instance.msg,
  'data': instance.data,
};

Data _$DataFromJson(Map<String, dynamic> json) => Data(
  titlePage: json['titlePage'] as String?,
  items:
      (json['items'] as List<dynamic>?)
          ?.map((e) => Item.fromJson(e as Map<String, dynamic>))
          .toList(),
  params:
      json['params'] == null
          ? null
          : Params.fromJson(json['params'] as Map<String, dynamic>),
  typeList: json['typeList'] as String?,
  appDomainFrontend: json['appDomainFrontend'] as String?,
  appDomainCdnImage: json['appDomainCdnImage'] as String?,
);

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
  'titlePage': instance.titlePage,
  'items': instance.items,
  'params': instance.params,
  'typeList': instance.typeList,
  'appDomainFrontend': instance.appDomainFrontend,
  'appDomainCdnImage': instance.appDomainCdnImage,
};

Item _$ItemFromJson(Map<String, dynamic> json) => Item(
  created:
      json['created'] == null
          ? null
          : Created.fromJson(json['created'] as Map<String, dynamic>),
  modified:
      json['modified'] == null
          ? null
          : Created.fromJson(json['modified'] as Map<String, dynamic>),
  id: json['_id'] as String?,
  name: json['name'] as String?,
  slug: json['slug'] as String?,
  originName: json['origin_name'] as String?,
  posterUrl: json['poster_url'] as String?,
  thumbUrl: json['thumb_url'] as String?,
  subDocquyen: json['sub_docquyen'] as bool?,
  chieurap: json['chieurap'] as bool?,
  time: json['time'] as String?,
  episodeCurrent: json['episode_current'] as String?,
  year: (json['year'] as num?)?.toInt(),
  category:
      (json['category'] as List<dynamic>?)
          ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
          .toList(),
  country:
      (json['country'] as List<dynamic>?)
          ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$ItemToJson(Item instance) => <String, dynamic>{
  'created': instance.created,
  'modified': instance.modified,
  '_id': instance.id,
  'name': instance.name,
  'slug': instance.slug,
  'origin_name': instance.originName,
  'poster_url': instance.posterUrl,
  'thumb_url': instance.thumbUrl,
  'sub_docquyen': instance.subDocquyen,
  'chieurap': instance.chieurap,
  'time': instance.time,
  'episode_current': instance.episodeCurrent,
  'year': instance.year,
  'category': instance.category,
  'country': instance.country,
};

Category _$CategoryFromJson(Map<String, dynamic> json) => Category(
  id: json['id'] as String?,
  name: json['name'] as String?,
  slug: json['slug'] as String?,
);

Map<String, dynamic> _$CategoryToJson(Category instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'slug': instance.slug,
};

Created _$CreatedFromJson(Map<String, dynamic> json) => Created(
  time: json['time'] == null ? null : DateTime.parse(json['time'] as String),
);

Map<String, dynamic> _$CreatedToJson(Created instance) => <String, dynamic>{
  'time': instance.time?.toIso8601String(),
};

Params _$ParamsFromJson(Map<String, dynamic> json) => Params(
  typeSlug: json['type_slug'] as String?,
  filterCategory:
      (json['filterCategory'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  filterCountry:
      (json['filterCountry'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  filterYear:
      (json['filterYear'] as List<dynamic>?)?.map((e) => e as String).toList(),
  filterType:
      (json['filterType'] as List<dynamic>?)?.map((e) => e as String).toList(),
  sortField: json['sortField'] as String?,
  sortType: json['sortType'] as String?,
);

Map<String, dynamic> _$ParamsToJson(Params instance) => <String, dynamic>{
  'type_slug': instance.typeSlug,
  'filterCategory': instance.filterCategory,
  'filterCountry': instance.filterCountry,
  'filterYear': instance.filterYear,
  'filterType': instance.filterType,
  'sortField': instance.sortField,
  'sortType': instance.sortType,
};
