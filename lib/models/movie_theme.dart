import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'movie_theme.g.dart';

@JsonSerializable()
class MovieData {
  bool? status;
  String? msg;
  Data? data;

  MovieData({this.status, this.msg, this.data});
  factory MovieData.fromJson(Map<String, dynamic> json) =>
      _$MovieDataFromJson(json);

  Map<String, dynamic> toJson() => _$MovieDataToJson(this);
}

@JsonSerializable()
class Data {
  String? titlePage;
  List<Item>? items;
  Params? params;
  String? typeList;
  String? appDomainFrontend;
  String? appDomainCdnImage;

  Data({
    this.titlePage,
    this.items,
    this.params,
    this.typeList,
    this.appDomainFrontend,
    this.appDomainCdnImage,
  });
  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class Item {
  Created? created;
  Created? modified;
  @JsonKey(name: '_id')
  String? id;
  String? name;
  String? slug;
  @JsonKey(name: 'origin_name')
  String? originName;
  @JsonKey(name: 'poster_url')
  String? posterUrl;
  @JsonKey(name: 'thumb_url')
  String? thumbUrl;
  @JsonKey(name: 'sub_docquyen')
  bool? subDocquyen;
  bool? chieurap;
  String? time;
  @JsonKey(name: 'episode_current')
  String? episodeCurrent;
  int? year;
  List<Category>? category;
  List<Category>? country;

  Item({
    this.created,
    this.modified,
    this.id,
    this.name,
    this.slug,
    this.originName,
    this.posterUrl,
    this.thumbUrl,
    this.subDocquyen,
    this.chieurap,
    this.time,
    this.episodeCurrent,
    this.year,
    this.category,
    this.country,
  });
  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);

  Map<String, dynamic> toJson() => _$ItemToJson(this);
}

@JsonSerializable()
class Category {
  String? id;
  String? name;
  String? slug;

  Category({this.id, this.name, this.slug});
  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryToJson(this);
}

@JsonSerializable()
class Created {
  DateTime? time;
  Created({this.time});

  factory Created.fromJson(Map<String, dynamic> json) =>
      _$CreatedFromJson(json);

  Map<String, dynamic> toJson() => _$CreatedToJson(this);
}

@JsonSerializable()
class Params {
  @JsonKey(name: 'type_slug')
  String? typeSlug;
  List<String>? filterCategory;
  List<String>? filterCountry;
  List<String>? filterYear;
  List<String>? filterType;
  String? sortField;
  String? sortType;

  Params({
    this.typeSlug,
    this.filterCategory,
    this.filterCountry,
    this.filterYear,
    this.filterType,
    this.sortField,
    this.sortType,
  });
  factory Params.fromJson(Map<String, dynamic> json) => _$ParamsFromJson(json);

  Map<String, dynamic> toJson() => _$ParamsToJson(this);
}
