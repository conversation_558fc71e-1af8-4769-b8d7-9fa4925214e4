// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'movie_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MoviesList _$MoviesList<PERSON>rom<PERSON>son(Map<String, dynamic> json) => MoviesList(
  status: json['status'] as bool,
  msg: json['msg'] as String?,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => Item.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$MoviesListToJson(MoviesList instance) =>
    <String, dynamic>{
      'status': instance.status,
      'msg': instance.msg,
      'items': instance.items,
    };

Item _$ItemFromJson(Map<String, dynamic> json) => Item(
  id: json['_id'] as String,
  name: json['name'] as String?,
  slug: json['slug'] as String?,
  originName: json['origin_name'] as String?,
  posterUrl: json['poster_url'] as String?,
  thumbUrl: json['thumb_url'] as String?,
);

Map<String, dynamic> _$ItemToJson(Item instance) => <String, dynamic>{
  '_id': instance.id,
  'name': instance.name,
  'slug': instance.slug,
  'origin_name': instance.originName,
  'poster_url': instance.posterUrl,
  'thumb_url': instance.thumbUrl,
};
