import 'package:json_annotation/json_annotation.dart';
part 'movie_list.g.dart';

@JsonSerializable()
class MoviesList {
  bool status;
  String? msg;
  List<Item> items;

  MoviesList({required this.status, required this.msg, required this.items});

  factory MoviesList.fromJson(Map<String, dynamic> json) =>
      _$MoviesListFromJson(json);

  Map<String, dynamic> toJson() => _$MoviesListToJson(this);
}

@JsonSerializable()
class Item {
  @JsonKey(name: '_id')
  String id;
  String? name;
  String? slug;
  @Json<PERSON>ey(name: 'origin_name')
  String? originName;
  @<PERSON><PERSON><PERSON>ey(name: 'poster_url')
  String? posterUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumb_url')
  String? thumbUrl;

  Item({
    required this.id,
    this.name,
    this.slug,
    this.originName,
    this.posterUrl,
    this.thumbUrl,
  });

  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);

  Map<String, dynamic> toJson() => _$ItemToJson(this);
}
