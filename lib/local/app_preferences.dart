import 'package:movie_app/core/contains.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppPreferences {
  static final AppPreferences _instance = AppPreferences._internal();
  static late SharedPreferences _prefs;

  AppPreferences._internal();

  static AppPreferences get instance => _instance;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static Future<void> setIsDarkMode(bool isDarkMode) async {
    await _prefs.setBool(Contains.isDarkMode, isDarkMode);
  }

  bool get isDarkMode => _prefs.getBool(Contains.isDarkMode) ?? false;

  static Future<void> setIsEnglish(bool isEnglish) async {
    await _prefs.setBool(Contains.isEnglish, isEnglish);
  }

  bool get isEnglish => _prefs.getBool(Contains.isEnglish) ?? true;
}
