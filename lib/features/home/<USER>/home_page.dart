import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_app/common/widget/content_widget.dart';
import 'package:movie_app/cubit/movie_cubit.dart';
import 'package:movie_app/cubit/movie_state.dart';
import 'package:movie_app/features/home/<USER>/movie_detail_page.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final PageController controller = PageController();
  late MovieCubit movieCubit;

  @override
  void initState() {
    super.initState();
    movieCubit = context.read();
    movieCubit.getMovies();
    movieCubit.getMoviesSingle();
    movieCubit.getMoviesSerie();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 61, 60, 60),
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 72, 75, 92),
        leading: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Image.asset('assets/images/logo.png'),
        ),
        title: Container(
          height: MediaQuery.of(context).size.height * 0.05,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white),
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            children: [
              const Expanded(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Search...",
                    style: TextStyle(color: Colors.white, fontSize: 15),
                  ),
                ),
              ),
              const Icon(Icons.search, color: Colors.white, size: 20),
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: BlocBuilder<MovieCubit, MovieState>(
          builder: (context, state) {
            return Column(
              children: [
                const SizedBox(height: 12),
                if (state.moviesList != null)
                  SizedBox(
                    height: 180,
                    child: PageView.builder(
                      controller: controller,
                      itemCount: state.moviesList?.items.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => MovieDetailPage(
                                      slug:
                                          state.moviesList?.items[index].slug ??
                                          '',
                                    ),
                              ),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: Image.network(
                                state.moviesList?.items[index].thumbUrl ?? '',
                                errorBuilder:
                                    (context, error, stackTrace) =>
                                        const Center(child: Icon(Icons.error)),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                const SizedBox(height: 10),

                if (state.moviesList != null)
                  SmoothPageIndicator(
                    controller: controller,
                    count: state.moviesList!.items.length,
                    effect: const WormEffect(
                      activeDotColor: Colors.red,
                      dotColor: Colors.grey,
                      dotHeight: 8,
                      dotWidth: 8,
                      spacing: 6,
                    ),
                  ),

                const SizedBox(height: 20),

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Text(
                          "Movie",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: 18,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 10),

                if (state.moviedetail != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.moviedetail?.data?.items?.length ?? 0,
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: 15,
                            crossAxisSpacing: 15,
                            childAspectRatio: 0.6,
                          ),
                      itemBuilder:
                          (context, index) => ContentWidget(
                            imageurl:
                                state
                                    .moviedetail!
                                    .data!
                                    .items![index]
                                    .posterUrl ??
                                '',
                            title:
                                state.moviedetail!.data!.items![index].name ??
                                '',
                          ),
                    ),
                  ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Text(
                          "Serie Movie",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: 18,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 10),
                if (state.movieserie != null)
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.movieserie?.data?.items?.length ?? 0,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      mainAxisExtent: 15,
                      crossAxisSpacing: 15,
                      childAspectRatio: 0.6,
                    ),
                    itemBuilder:
                        (context, index) => ContentWidget(
                          title:
                              state.movieserie!.data!.items![index].name ?? '',
                          imageurl:
                              state.movieserie!.data!.items![index].posterUrl ??
                              '',
                        ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
