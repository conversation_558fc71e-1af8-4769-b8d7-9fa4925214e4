import 'package:flutter/material.dart';

class ContentWidget extends StatelessWidget {
  final String title;
  final String imageurl;

  const ContentWidget({super.key, required this.title, required this.imageurl});

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    final fullUrl =
        imageurl.startsWith("http")
            ? imageurl
            : "https://phimimg.com/${imageurl.startsWith("/") ? imageurl.substring(1) : imageurl}";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: size.height * 0.2,
          width: size.width * 0.5,
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(fullUrl),
                fit: BoxFit.cover,
              ),
              color: Colors.blue,
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
        const SizedBox(height: 6),
        Text(
          title,
          style: const TextStyle(color: Colors.white),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
