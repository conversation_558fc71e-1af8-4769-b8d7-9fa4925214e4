import 'package:flutter/material.dart';
import 'package:movie_app/features/home/<USER>/home_page.dart';
import 'package:movie_app/features/settings/views/settings_page.dart';

class MyHomePage extends StatefulWidget {
  MyHomePage({super.key, this.index = 0});
  int? index;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int indexSelected = 0;
  List<Widget> pages = [HomePage(), SettingsPage()];

  @override
  void initState() {

    super.initState();
    indexSelected = widget.index ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: pages[indexSelected],
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: Colors.indigo,
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,
        onTap: (value) {
          setState(() {
            indexSelected = value;
          });
        },
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: "Home"),
          BottomNavigationBarItem(icon: Icon(Icons.settings), label: "Setting"),
        ],
      ),
    );
  }
}
