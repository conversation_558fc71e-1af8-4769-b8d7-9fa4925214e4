import 'package:dio/dio.dart';
import 'package:logger/web.dart';
import 'package:movie_app/core/contains.dart';
import 'package:movie_app/local/app_preferences.dart';
import 'package:movie_app/models/movie_list.dart';
import 'package:movie_app/models/movie_theme.dart';

class MovieServices {
  MovieServices._();

  static final Logger logger = Logger();

  static final Dio dio = Dio(
    BaseOptions(baseUrl: Contains.baseUrl, contentType: 'application/json'),
  );

  static Future<MoviesList> getMovies() async {
    try {
      final response = await dio.get(
        'danh-sach/phim-moi-cap-nhat',
        queryParameters: {'page': 1},
      );
      return MoviesList.fromJson(response.data);
    } catch (e) {
      logger.e(e);
      throw Exception('Đã có lỗi xảy ra!');
    }
  }

  static Future<MovieData> getMoviesByTheme(String endpoint) async {
    try {
      final response = await dio.get(endpoint);
      return MovieData.from<PERSON>son(response.data);
    } catch (e) {
      logger.e(e);
      throw Exception("Error Occured");
    }
  }
}
