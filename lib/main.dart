import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:movie_app/cubit/movie_cubit.dart';
import 'package:movie_app/cubit/settings_cubit.dart';
import 'package:movie_app/features/home/<USER>/home_page.dart';
import 'package:movie_app/generated/l10n.dart';
import 'package:movie_app/local/app_preferences.dart';
import 'package:movie_app/my_home_page.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppPreferences.init();
  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SettingsCubit()),
        BlocProvider(create: (context) => MovieCubit()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    context.read<SettingsCubit>().getIsDarkMode();
    context.read<SettingsCubit>().getIsEnglish();
  }

  Future<void> initData() async {}

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme:
          context.watch<SettingsCubit>().state.isDarkMode
              ? ThemeData.dark()
              : ThemeData.light(),
      locale: context.watch<SettingsCubit>().state.locale,
      localizationsDelegates: [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: S.delegate.supportedLocales,
      home: MyHomePage(),
    );
  }
}
